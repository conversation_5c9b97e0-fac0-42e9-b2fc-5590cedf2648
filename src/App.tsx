import React, { useState } from 'react';
import ChatComponent from './ChatComponent';
import UploadComponent from './UploadComponent';
import EmbedComponent from './EmbedComponent';
import SettingsComponent from './SettingsComponent';
import SimpleSidebar from './SimpleSidebar';
import { BackendStatusProvider, useBackendStatus } from './contexts/BackendStatusContext';

// Componente interno que tem acesso ao contexto
const AppContent: React.FC = () => {
  const [activeSection, setActiveSection] = useState('chat');
  const { checkStatus } = useBackendStatus();

  // Verificar status quando muda de secção
  const handleSectionChange = (section: string) => {
    setActiveSection(section);
    checkStatus(); // Verificar status imediatamente
  };

  const renderContent = () => {
    switch (activeSection) {
      case 'chat':
      case 'database':
        return <ChatComponent />;
      case 'upload':
      case 'storage':
        return <UploadComponent />;
      case 'embed':
      case 'integrations':
        return <EmbedComponent />;
      case 'settings':
        return <SettingsComponent />;
      default:
        return (
          <div className="p-8">
            <h2 className="text-2xl font-bold text-white mb-4">
              {activeSection.charAt(0).toUpperCase() + activeSection.slice(1)}
            </h2>
            <p className="text-gray-300">Esta funcionalidade está em desenvolvimento...</p>
          </div>
        );
    }
  };

  return (
    <div style={{ display: 'flex', height: '100vh', backgroundColor: '#0f0f0f' }}>
      <SimpleSidebar
        activeSection={activeSection}
        onSectionChange={handleSectionChange}
      />
      <main style={{ flex: 1, overflowY: 'auto', backgroundColor: '#0f0f0f' }}>
        {renderContent()}
      </main>
    </div>
  );
};

// Componente principal que fornece o contexto
function App() {
  return (
    <BackendStatusProvider>
      <AppContent />
    </BackendStatusProvider>
  );
}

export default App;
