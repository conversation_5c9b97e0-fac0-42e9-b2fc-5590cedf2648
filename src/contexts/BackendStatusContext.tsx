import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

export type BackendStatus = 'online' | 'offline' | 'checking';

interface BackendStatusContextType {
  status: BackendStatus;
  checkStatus: () => Promise<void>;
}

const BackendStatusContext = createContext<BackendStatusContextType | undefined>(undefined);

export const useBackendStatus = () => {
  const context = useContext(BackendStatusContext);
  if (context === undefined) {
    throw new Error('useBackendStatus must be used within a BackendStatusProvider');
  }
  return context;
};

interface BackendStatusProviderProps {
  children: ReactNode;
}

export const BackendStatusProvider: React.FC<BackendStatusProviderProps> = ({ children }) => {
  const [status, setStatus] = useState<BackendStatus>('checking');

  const checkStatus = async () => {
    try {
      // Só mostrar "checking" se o status atual for "checking" (primeira vez)
      if (status === 'checking') {
        setStatus('checking');
      }

      // Tentar conectar ao backend com timeout mais curto
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 2000); // 2 segundos timeout

      const response = await fetch('http://localhost:8000/health', {
        method: 'GET',
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
        },
        cache: 'no-cache', // Evitar cache
      });

      clearTimeout(timeoutId);

      if (response.ok) {
        setStatus('online');
      } else {
        setStatus('offline');
      }
    } catch (error) {
      // Não fazer log para evitar spam no console
      setStatus('offline');
    }
  };

  // Verificar status ao carregar
  useEffect(() => {
    checkStatus();

    // Verificar status a cada 3 segundos para resposta mais rápida
    const interval = setInterval(checkStatus, 3000);

    // Verificar status quando a janela ganha foco
    const handleFocus = () => {
      checkStatus();
    };

    window.addEventListener('focus', handleFocus);

    return () => {
      clearInterval(interval);
      window.removeEventListener('focus', handleFocus);
    };
  }, []);

  const value = {
    status,
    checkStatus,
  };

  return (
    <BackendStatusContext.Provider value={value}>
      {children}
    </BackendStatusContext.Provider>
  );
};
